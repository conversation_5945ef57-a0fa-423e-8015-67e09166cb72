<!DOCTYPE html>
<html>
<head>
    <title>Function Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>ESL App Function Test</h1>
    <div id="test-results"></div>
    
    <h2>Manual Tests</h2>
    <button onclick="testStartLesson()">Test Start Lesson</button>
    <button onclick="testScoreboard()">Test Scoreboard</button>
    <button onclick="testShowActivity()">Test Show Activity</button>
    
    <script src="script.js"></script>
    <script>
        function addTestResult(testName, passed, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `<strong>${testName}:</strong> ${passed ? 'PASS' : 'FAIL'} - ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function runTests() {
            // Test 1: Check if global variables are defined
            try {
                addTestResult('Global Variables', 
                    typeof currentActivity !== 'undefined' && typeof scores !== 'undefined',
                    'Global variables are properly defined');
            } catch (e) {
                addTestResult('Global Variables', false, e.message);
            }
            
            // Test 2: Check if main functions exist
            const functions = ['startLesson', 'showActivity', 'backToMenu', 'adjustScore', 'resetScores'];
            functions.forEach(funcName => {
                addTestResult(`Function ${funcName}`, 
                    typeof window[funcName] === 'function',
                    `Function ${funcName} is defined`);
            });
            
            // Test 3: Check if data arrays are populated
            addTestResult('Puzzles Data', 
                Array.isArray(puzzles) && puzzles.length > 0,
                `Puzzles array has ${puzzles ? puzzles.length : 0} items`);
                
            addTestResult('Vocab Questions Data', 
                Array.isArray(vocabQuestions) && vocabQuestions.length > 0,
                `Vocab questions array has ${vocabQuestions ? vocabQuestions.length : 0} items`);
        }
        
        function testStartLesson() {
            try {
                startLesson();
                addTestResult('Start Lesson Function', true, 'Function executed without errors');
            } catch (e) {
                addTestResult('Start Lesson Function', false, e.message);
            }
        }
        
        function testScoreboard() {
            try {
                adjustScore('red', 5);
                addTestResult('Scoreboard Function', true, 'Score adjustment executed without errors');
            } catch (e) {
                addTestResult('Scoreboard Function', false, e.message);
            }
        }
        
        function testShowActivity() {
            try {
                showActivity('puzzles');
                addTestResult('Show Activity Function', true, 'Function executed without errors');
            } catch (e) {
                addTestResult('Show Activity Function', false, e.message);
            }
        }
        
        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
