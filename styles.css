body {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 100vh;
    background-color: #f0f2f5;
    color: #333;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

#main-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 1200px;
    margin-top: 20px; /* Space for scoreboard */
    box-sizing: border-box;
    position: relative;
}

.screen {
    display: none;
    text-align: center;
}

.screen.active {
    display: block;
}

h1 {
    color: #4a90e2;
    margin-bottom: 10px;
}

h2 {
    color: #333;
    margin-bottom: 20px;
}

.start-btn {
    background-color: #4CAF50;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.2em;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 20px;
}

.start-btn:hover {
    background-color: #45a049;
}

.basketball-animation {
    margin: 20px auto;
    width: 200px;
    height: 200px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-30px); }
}

/* Activity Menu */
.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.activity-btn {
    background-color: #e0f2f7;
    border: 2px solid #a7d9ed;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 150px;
}

.activity-btn:hover {
    background-color: #d0efff;
    border-color: #7bc8e6;
    transform: translateY(-5px);
}

.activity-btn span {
    font-size: 3em;
    margin-bottom: 10px;
}

.activity-btn h3 {
    margin: 0;
    color: #2c3e50;
}

.activity-btn p {
    font-size: 0.9em;
    color: #7f8c8d;
}

.back-btn {
    background-color: #f39c12;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 30px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background-color: #e67e22;
}

/* Scoreboard */
.scoreboard {
    width: 100%;
    background-color: #34495e;
    color: white;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.scoreboard h2 {
    margin: 0;
    font-size: 1.5em;
    color: white;
}

.scoreboard .teams {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.scoreboard .team {
    display: flex;
    align-items: center;
    gap: 5px;
}

.scoreboard .team-name {
    font-weight: bold;
    cursor: pointer;
}

.scoreboard .score {
    background-color: white;
    color: #34495e;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
}

.scoreboard button {
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 3px 8px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.scoreboard button:hover {
    background-color: #27ae60;
}

.scoreboard .reset-all-btn {
    background-color: #e74c3c;
}

.scoreboard .reset-all-btn:hover {
    background-color: #c0392b;
}

/* Teacher Controls */
.teacher-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #34495e;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.teacher-controls button {
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.teacher-controls button:hover {
    background-color: #2980b9;
}

/* Activity Specific Styles */
.activity {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-top: 20px;
}

/* Logic Puzzles */
.puzzle-controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.puzzle-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.puzzle-controls button:hover {
    background-color: #2980b9;
}

.puzzle-image svg {
    max-width: 100%;
    height: auto;
    margin: 20px auto;
    display: block;
}

.sequence-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.sequence-item {
    background-color: #f8f8f8;
    border: 1px solid #ccc;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.sequence-item:hover {
    background-color: #e8e8e8;
    border-color: #999;
}

.sequence-item.selected {
    background-color: #3498db !important;
    color: white !important;
    border-color: #2980b9;
}

.sequence-answer {
    background-color: #f9f9f9;
    border: 2px dashed #ccc;
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
    min-height: 60px;
}

.sequence-answer p {
    margin: 0 0 10px 0;
    font-weight: bold;
    color: #666;
}

.mc-options button {
    background-color: #3498db;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    margin: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.mc-options button:hover {
    background-color: #2980b9;
}

/* Matching Game */
.matching-container {
    margin: 20px 0;
}

.matching-instructions {
    font-weight: bold;
    margin-bottom: 15px;
    color: #666;
}

.matching-columns {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin: 20px 0;
}

.matching-left, .matching-right {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 200px;
}

.match-item {
    background-color: #ecf0f1;
    border: 2px solid #bdc3c7;
    padding: 12px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: bold;
}

.match-item:hover {
    background-color: #d5dbdb;
    border-color: #95a5a6;
}

.match-item.selected {
    background-color: #3498db;
    color: white;
    border-color: #2980b9;
}

.match-item.matched {
    background-color: #2ecc71;
    color: white;
    border-color: #27ae60;
    cursor: default;
}

.match-item.wrong {
    background-color: #e74c3c;
    color: white;
    border-color: #c0392b;
}

/* Memory Game */
.memory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 600px;
    margin: 20px auto;
}

.memory-card {
    background-color: #f39c12;
    color: white;
    padding: 20px;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    aspect-ratio: 1 / 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.memory-card:hover {
    background-color: #e67e22;
}

.memory-card.flipped {
    background-color: #2ecc71;
}

.memory-card.matched {
    background-color: #27ae60;
    cursor: default;
}

.memory-controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.memory-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.memory-controls button:hover {
    background-color: #2980b9;
}

/* Vocab Quiz */
.question-icon {
    font-size: 4em;
    margin-bottom: 10px;
}

.quiz-option {
    background-color: #9b59b6;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    margin: 8px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background-color 0.3s ease;
}

.quiz-option:hover {
    background-color: #8e44ad;
}

.quiz-option.correct {
    background-color: #2ecc71;
}

.quiz-option.incorrect {
    background-color: #e74c3c;
}

.quiz-controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.quiz-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quiz-controls button:hover {
    background-color: #2980b9;
}

/* Speed Reading */
.speed-reading-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
    align-items: center;
}

.speed-reading-controls label {
    font-weight: bold;
}

.speed-reading-controls input[type="range"] {
    width: 150px;
}

.speed-reading-controls button {
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.speed-reading-controls button:hover {
    background-color: #2980b9;
}

.reading-text {
    font-size: 1.4em;
    line-height: 1.8;
    text-align: justify;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fdfdfd;
}

.reading-text .word {
    transition: background-color 0.1s linear;
    padding: 2px 0;
}

.reading-text .word.highlight {
    background-color: yellow;
    border-radius: 3px;
}

.comprehension-question {
    background-color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
}

.comp-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 10px;
}

.comp-options button {
    background-color: #9b59b6;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.comp-options button:hover {
    background-color: #8e44ad;
}

.comp-options button.correct {
    background-color: #2ecc71;
}

.comp-options button.incorrect {
    background-color: #e74c3c;
}

#gap-fill-text {
    font-size: 1.2em;
    line-height: 1.6;
    text-align: justify;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fdfdfd;
    margin: 20px 0;
}

.gap-word {
    display: inline-block;
    min-width: 80px;
    text-align: center;
    border-bottom: 2px dashed #999;
    margin: 0 5px;
    cursor: pointer;
    font-weight: bold;
    color: #3498db;
}

.gap-word.revealed-gap {
    border-bottom: 2px solid #2ecc71;
    color: #2ecc71;
    cursor: default;
}

/* Minefield */
.minefield-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.team-lives {
    display: flex;
    gap: 15px;
}

.team-life {
    font-weight: bold;
}

.minefield-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 5px;
    max-width: 600px;
    margin: 20px auto;
}

.minefield-cell {
    background-color: #f1c40f;
    color: #333;
    padding: 15px;
    border-radius: 5px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    aspect-ratio: 1 / 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.minefield-cell:hover {
    background-color: #f39c12;
}

.minefield-cell.revealed {
    background-color: #ecf0f1;
    cursor: default;
}

.minefield-cell.bomb {
    background-color: #e74c3c;
    color: white;
}

.minefield-controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.minefield-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.minefield-controls button:hover {
    background-color: #2980b9;
}

/* Tic-Tac-Toe */
.ttt-info {
    margin-bottom: 20px;
}

.ttt-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    max-width: 700px;
    margin: 20px auto;
}

.ttt-cell {
    background-color: #bdc3c7;
    color: #333;
    padding: 10px;
    border-radius: 5px;
    font-size: 0.9em;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    aspect-ratio: 1 / 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.ttt-cell:hover {
    background-color: #95a5a6;
}

.ttt-cell.red {
    background-color: #e74c3c;
    color: white;
}

.ttt-cell.blue {
    background-color: #3498db;
    color: white;
}

.ttt-cell.green {
    background-color: #2ecc71;
    color: white;
}

.ttt-cell.yellow {
    background-color: #f1c40f;
    color: #333;
}

.ttt-controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.ttt-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.ttt-controls button:hover {
    background-color: #2980b9;
}

/* Jeopardy */
.jeopardy-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 20px auto;
    max-width: 800px;
}

.jeopardy-category {
    background-color: #2c3e50;
    color: white;
    padding: 15px 10px;
    font-weight: bold;
    text-transform: uppercase;
    border-radius: 5px;
    text-align: center;
}

.jeopardy-cell {
    background-color: #3498db;
    color: white;
    padding: 20px;
    border-radius: 5px;
    font-size: 1.5em;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    aspect-ratio: 1 / 1;
}

.jeopardy-cell:hover {
    background-color: #2980b9;
}

.jeopardy-cell.used {
    background-color: #7f8c8d;
    cursor: default;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 700px;
    text-align: center;
}

.modal-options button {
    background-color: #9b59b6;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    margin: 8px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background-color 0.3s ease;
}

.modal-options button:hover {
    background-color: #8e44ad;
}

.modal-options button.correct {
    background-color: #2ecc71;
}

.modal-options button.incorrect {
    background-color: #e74c3c;
}

.modal-controls button {
    background-color: #f39c12;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 20px;
    transition: background-color 0.3s ease;
}

.modal-controls button:hover {
    background-color: #e67e22;
}

.timer {
    font-size: 1.5em;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 10px;
}

/* Family Feud */
.feud-board {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    margin: 20px auto;
    max-width: 600px;
}

.feud-answer {
    background-color: #ecf0f1;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.feud-answer:hover {
    background-color: #dfe6e9;
    border-color: #95a5a6;
}

.feud-answer.revealed {
    background-color: #2ecc71;
    color: white;
    border-color: #27ae60;
    cursor: default;
}

.feud-points {
    background-color: #3498db;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9em;
}

.feud-controls button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px;
    transition: background-color 0.3s ease;
}

.feud-controls button:hover {
    background-color: #2980b9;
}

.hidden {
    display: none;
}


